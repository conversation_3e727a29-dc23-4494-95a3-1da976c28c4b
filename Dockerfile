# Multi-stage build for smaller final image
FROM python:3.11-slim as builder

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml ./
COPY uv.lock* ./

# Install dependencies
RUN uv sync --frozen --no-cache --no-dev

# Production stage
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/

# Set working directory
WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /app/.venv /app/.venv

# Copy application code
COPY src/ ./src/
COPY grpc_generated/ ./grpc_generated/
COPY proto/ ./proto/
COPY pyproject.toml ./

# Ensure virtual environment is in PATH
ENV PATH="/app/.venv/bin:$PATH"

# Install the package in the virtual environment
RUN uv pip install -e .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# Expose HTTP port
EXPOSE 8000

# Environment variables
ENV HTTP_HOST=0.0.0.0
ENV HTTP_PORT=8000
ENV LOG_LEVEL=INFO

# Health check for HTTP mode
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the HTTP application
CMD ["context-engine-mcp"]