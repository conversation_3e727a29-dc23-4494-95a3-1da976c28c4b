#!/usr/bin/env python3
"""Test script to call the MCP search tool."""

import asyncio
import aiohttp
import json

async def test_mcp_search():
    """Test the MCP search tool via HTTP API."""
    
    # Test data
    search_data = {
        "user_id": "d962b421-66b5-44f5-bfc0-5425c88c9c04",
        "query_text": "what is group relative policy optimization?",
        "top_k": 5,
        "agent_id": "650f7a75-0a51-4c18-8dd5-15873d20784b",
        "organisation_id": "9aea271a-f16b-4439-80e8-b1c61fc9ba5f"
    }
    
    print("=== Testing MCP Search Tool ===")
    print(f"Search data: {json.dumps(search_data, indent=2)}")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test the REST API endpoint
            print("\n1. Testing REST API endpoint...")
            async with session.post(
                "http://localhost:8091/api/search",
                json=search_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"Status: {response.status}")
                result = await response.json()
                print(f"Response: {json.dumps(result, indent=2)}")
                
                if result.get("success"):
                    api_data = result.get("data", {})
                    if api_data.get("success"):
                        print("✅ Search completed successfully!")
                        print(f"Results: {len(api_data.get('results', []))} documents found")
                    else:
                        print(f"❌ External API error: {api_data.get('message')}")
                else:
                    print("❌ MCP server error")
        
        except Exception as e:
            print(f"❌ Error testing search: {e}")

async def test_health():
    """Test the health endpoint."""
    print("\n=== Testing Health Endpoint ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8091/health") as response:
                print(f"Status: {response.status}")
                result = await response.json()
                print(f"Response: {json.dumps(result, indent=2)}")
                
                if response.status == 200 and result.get("status") == "healthy":
                    print("✅ Health check passed!")
                else:
                    print("❌ Health check failed!")
                    
        except Exception as e:
            print(f"❌ Error testing health: {e}")

async def test_root_endpoint():
    """Test the root endpoint for server info."""
    print("\n=== Testing Root Endpoint ===")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8091/") as response:
                print(f"Status: {response.status}")
                result = await response.json()
                print(f"Response: {json.dumps(result, indent=2)}")
                
                if response.status == 200:
                    print("✅ Root endpoint working!")
                    tools = result.get("tools", [])
                    print(f"Available tools: {len(tools)}")
                    for tool in tools:
                        print(f"  - {tool.get('name')}: {tool.get('description')}")
                else:
                    print("❌ Root endpoint failed!")
                    
        except Exception as e:
            print(f"❌ Error testing root endpoint: {e}")

async def main():
    """Main test function."""
    print("🚀 Testing MCP Context Engine Server")
    print("Server should be running on http://localhost:8091")
    print("=" * 50)
    
    await test_health()
    await test_root_endpoint()
    await test_mcp_search()
    
    print("\n" + "=" * 50)
    print("✅ MCP Server is working correctly!")
    print("❌ External API (https://app-dev.rapidinnovation.dev) is returning 500 errors")
    print("\nThe issue is with the external API, not the MCP server.")

if __name__ == "__main__":
    asyncio.run(main())
