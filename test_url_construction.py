#!/usr/bin/env python3
"""Test script for URL construction."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for environment variables."""
    
    # Organisation Service Configuration
    ORGANISATION_SERVICE_HOST: str = os.getenv("ORGANISATION_SERVICE_HOST", "localhost")
    ORGANISATION_SERVICE_PORT: int = int(os.getenv("ORGANISATION_SERVICE_PORT", "50070"))
    ORGANISATION_SERVICE_AUTH_KEY: str = os.getenv("ORGANISATION_SERVICE_AUTH_KEY", "hCbxAlpjFyXPd1UiJwZqTWgC20DrMb6YDxN7trz7OAB")
    
    @classmethod
    def get_api_base_url(cls) -> str:
        """Get the REST API base URL, respecting existing scheme and adding port if needed."""
        host = cls.ORGANISATION_SERVICE_HOST
        port = cls.ORGANISATION_SERVICE_PORT
        
        if "://" in host:  # Scheme (http:// or https://) is already present
            # Check if port is already in the URL
            host_part = host.split("://")[1]
            if ":" in host_part:
                # Port already specified in host
                return host
            else:
                # Only add port if it's not the default port for the scheme
                scheme = host.split("://")[0]
                if (scheme == "https" and port == 443) or (scheme == "http" and port == 80):
                    return host
                else:
                    return f"{host}:{port}"
        else:  # No scheme, default to http
            return f"http://{host}:{port}"

def test_url_construction():
    """Test URL construction with different scenarios."""
    print("=== URL Construction Test ===")
    
    print(f"ORGANISATION_SERVICE_HOST: {Config.ORGANISATION_SERVICE_HOST}")
    print(f"ORGANISATION_SERVICE_PORT: {Config.ORGANISATION_SERVICE_PORT}")
    print(f"Constructed base URL: {Config.get_api_base_url()}")
    
    # Test the full URL that would be used for search
    base_url = Config.get_api_base_url()
    search_url = f"{base_url}/api/v1/google-drive/search"
    print(f"Full search URL: {search_url}")
    
    # Test different scenarios
    test_cases = [
        ("https://app-dev.rapidinnovation.dev", 8000),
        ("https://app-dev.rapidinnovation.dev", 443),
        ("http://localhost", 8000),
        ("http://localhost", 80),
        ("localhost", 8000),
        ("app-dev.rapidinnovation.dev:8080", 8000),
    ]
    
    print("\n=== Testing different URL scenarios ===")
    for host, port in test_cases:
        # Temporarily override config
        original_host = Config.ORGANISATION_SERVICE_HOST
        original_port = Config.ORGANISATION_SERVICE_PORT
        
        Config.ORGANISATION_SERVICE_HOST = host
        Config.ORGANISATION_SERVICE_PORT = port
        
        result_url = Config.get_api_base_url()
        print(f"Host: {host:35} Port: {port:4} -> {result_url}")
        
        # Restore original values
        Config.ORGANISATION_SERVICE_HOST = original_host
        Config.ORGANISATION_SERVICE_PORT = original_port

if __name__ == "__main__":
    test_url_construction()
