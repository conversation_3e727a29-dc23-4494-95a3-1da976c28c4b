#!/usr/bin/env python3
"""Test script for API client URL construction and basic functionality."""

import asyncio
import logging
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import modules directly to avoid server dependencies
import importlib.util

# Load config module
config_spec = importlib.util.spec_from_file_location(
    "config",
    os.path.join(os.path.dirname(__file__), 'src', 'context_engine_mcp', 'helper', 'config.py')
)
config_module = importlib.util.module_from_spec(config_spec)
config_spec.loader.exec_module(config_module)
Config = config_module.Config

# Load api_client module
api_client_spec = importlib.util.spec_from_file_location(
    "api_client",
    os.path.join(os.path.dirname(__file__), 'src', 'context_engine_mcp', 'providers', 'api_client.py')
)
api_client_module = importlib.util.module_from_spec(api_client_spec)
api_client_spec.loader.exec_module(api_client_module)
ContextAPIClient = api_client_module.ContextAPIClient

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_url_construction():
    """Test URL construction."""
    print("Testing URL construction...")
    print(f"ORGANISATION_SERVICE_HOST: {Config.ORGANISATION_SERVICE_HOST}")
    print(f"ORGANISATION_SERVICE_PORT: {Config.ORGANISATION_SERVICE_PORT}")
    print(f"Constructed base URL: {Config.get_api_base_url()}")
    
async def test_api_client():
    """Test API client basic functionality."""
    print("\nTesting API client...")
    
    client = ContextAPIClient()
    
    try:
        # Test connection
        await client.connect()
        print("✓ API client connected successfully")
        
        # Test search with minimal data
        test_request = {
            "user_id": "test-user",
            "query_text": "test query",
            "top_k": 5,
            "agent_id": "test-agent",
            "organisation_id": "test-org",
            "file_ids": []
        }
        
        print(f"Testing search with request: {test_request}")
        result = await client.search_similar_documents(test_request)
        print(f"Search result: {result}")
        
    except Exception as e:
        print(f"✗ Error testing API client: {e}")
        logger.error(f"API client test failed: {e}", exc_info=True)
    finally:
        await client.close()
        print("✓ API client closed")

async def main():
    """Main test function."""
    print("=== API Client Test ===")
    
    try:
        Config.validate()
        print("✓ Configuration validation passed")
    except Exception as e:
        print(f"✗ Configuration validation failed: {e}")
        return
    
    await test_url_construction()
    await test_api_client()
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(main())
