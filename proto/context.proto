syntax = "proto3";

package context;

// Context Engine REST API integration schemas
// Note: This proto file now defines data structures for REST API communication
// instead of gRPC service definitions

// Request to search for semantically similar documents
message SearchSimilarDocumentsRequest {
  string user_id = 1;
  string query_text = 2;
  int32 top_k = 3;  // Number of results to return
  string agent_id = 4;  // Optional agent ID to filter results by department access
  string organisation_id = 5;  // Organization ID (mandatory for production use)
  repeated string file_ids = 6;  // Optional list of specific file IDs to search within
}

// Entity information from knowledge graph
message EntityInfo {
  string id = 1;
  string name = 2;
  string type = 3;
  map<string, string> properties = 4;  // Additional entity properties
}

// Relationship information from knowledge graph
message RelationshipInfo {
  string id = 1;
  string type = 2;
  string source_entity_id = 3;
  string target_entity_id = 4;
  map<string, string> properties = 5;  // Additional relationship properties
}

// Search result item with enhanced metadata
message SearchResultItem {
  string file_id = 1;
  string file_name = 2;
  string mime_type = 3;
  string web_view_link = 4;
  string created_time = 5;
  string modified_time = 6;
  float score = 7;  // Similarity score
  string vector_id = 8;  // ID of the vector in Pinecone
  string chunk_text = 9;  // The actual text content of the matched chunk
  string search_type = 10;  // Type of search performed (e.g., "hybrid", "semantic", "graph")
  
  // Knowledge graph context for this chunk
  repeated EntityInfo entities = 11;  // Entities found in this chunk
  repeated RelationshipInfo relationships = 12;  // Relationships found in this chunk
  
  // Enhanced search metadata
  int32 chunk_index = 13;  // Index of the chunk within the document
  string document_section = 14;  // Section/chapter of the document
  map<string, string> metadata = 15;  // Additional metadata from the document
  string relevance_explanation = 16;  // AI-generated explanation of why this result is relevant
}

// Enhanced response for semantic search operation
message SearchSimilarDocumentsResponse {
  bool success = 1;
  string message = 2;
  repeated SearchResultItem results = 3;
  
  // Enhanced response metadata
  string query_id = 4;  // Unique identifier for this search query
  int64 search_timestamp = 5;  // Unix timestamp when search was performed
  int32 total_results_found = 6;  // Total number of results before top_k filtering
  float search_duration_ms = 7;  // Time taken to perform the search in milliseconds
  string search_strategy = 8;  // Strategy used for search (e.g., "semantic", "hybrid", "graph-enhanced")
  map<string, string> search_metadata = 9;  // Additional search-specific metadata
}

// Request to batch search for semantically similar documents
message BatchSearchSimilarDocumentsRequest {
  string user_id = 1;
  repeated string query_texts = 2;
  int32 top_k = 3;  // Number of results to return per query
  string agent_id = 4;  // Optional agent ID to filter results by department access
  string organisation_id = 5;  // Organization ID (mandatory for production use)
  repeated string file_ids = 6;  // Optional list of specific file IDs to search within
}

// Response for batch semantic search operation
message BatchSearchSimilarDocumentsResponse {
  bool success = 1;
  string message = 2;
  repeated QueryResults query_results = 3;
}

// Results for a single query in a batch
message QueryResults {
  string query_text = 1;  // The original query text
  repeated SearchResultItem results = 2;  // Search results for this query
}

// Request to sync a specific Google Drive file by URL
message SyncFileByUrlRequest {
  repeated string drive_url = 1;       // Google Drive URLs
  string agent_id = 2;        // Agent ID
  string user_id = 3;         // Optional user ID
  string organisation_id = 4;  // Organisation ID
}

// Information about a synced file
message SyncedFileInfo {
  string file_id = 1;         // The ID of the synced file
  string file_name = 2;       // The name of the synced file
  string drive_url = 3;       // The original URL that was synced
  string sync_status = 4;     // "completed" or "failed"
  string error_message = 5;   // Error message if sync failed
}

// Response for sync file by URL operation
message SyncFileByUrlResponse {
  bool success = 1;
  string message = 2;
  repeated SyncedFileInfo synced_files = 3;  // Information about all synced files
  int32 total_files = 4;      // Total number of URLs processed
  int32 successful_syncs = 5; // Number of successful syncs
  int32 failed_syncs = 6;     // Number of failed syncs
}