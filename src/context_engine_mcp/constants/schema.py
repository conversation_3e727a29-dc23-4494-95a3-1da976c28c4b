"""Pydantic schemas for Context Engine MCP Server."""

from typing import List, Optional
from pydantic import BaseModel, Field


class SearchSchema(BaseModel):
    """Schema for search tool."""
    user_id: str = Field(..., min_length=1, description="User ID")
    query_text: str = Field(..., min_length=1, description="Search query text")
    top_k: int = Field(default=5, ge=1, le=100, description="Number of results to return")
    agent_id: Optional[str] = Field(default=None, description="Optional agent ID to filter results by department access")
    organisation_id: str = Field(..., min_length=1, description="Organization ID (mandatory for production use)")
    file_ids: Optional[List[str]] = Field(default=None, description="Optional list of specific file IDs to search within")


class BatchSearchSchema(BaseModel):
    """Schema for batch search tool."""
    user_id: str = Field(..., min_length=1, description="User ID")
    query_texts: List[str] = Field(..., min_items=1, description="List of search query texts")
    top_k: int = Field(default=5, ge=1, le=100, description="Number of results to return per query")
    agent_id: Optional[str] = Field(default=None, description="Optional agent ID to filter results by department access")
    organisation_id: str = Field(..., min_length=1, description="Organization ID (mandatory for production use)")
    file_ids: Optional[List[str]] = Field(default=None, description="Optional list of specific file IDs to search within")


class UploadFileSchema(BaseModel):
    """Schema for upload file by URL tool."""
    drive_url: str = Field(..., min_length=1, description="Google Drive URL")
    agent_id: str = Field(..., min_length=1, description="Agent ID")
    user_id: Optional[str] = Field(default=None, description="Optional user ID")
    organisation_id: str = Field(..., min_length=1, description="Organisation ID")