"""Enums for Context Engine MCP Server."""

from enum import Enum


class Tools(str, Enum):
    """Available MCP tools."""
    SEARCH = "search"
    BATCH_SEARCH = "batch_search"
    UPLOAD_FILE_BY_URL = "upload_file_by_url"


class SearchType(str, Enum):
    """Search types supported by the service."""
    SEMANTIC = "semantic"
    HYBRID = "hybrid"
    GRAPH = "graph"


class SyncStatus(str, Enum):
    """File sync status values."""
    COMPLETED = "completed"
    IN_PROGRESS = "in_progress"
    FAILED = "failed"