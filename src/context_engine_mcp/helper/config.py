"""Configuration management for Context Engine MCP Server."""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Configuration class for environment variables."""
    
    # Organisation Service Configuration
    ORGANISATION_SERVICE_HOST: str = os.getenv("ORGANISATION_SERVICE_HOST", "localhost")
    ORGANISATION_SERVICE_PORT: int = int(os.getenv("ORGANISATION_SERVICE_PORT", "50070"))
    ORGANISATION_SERVICE_AUTH_KEY: str = os.getenv("ORGANISATION_SERVICE_AUTH_KEY", "hCbxAlpjFyXPd1UiJwZqTWgC20DrMb6YDxN7trz7OAB")
    
    # Redis Configuration
    REDIS_HOST: str = os.getenv("REDIS_HOST", "**************")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")
    
    # Server Configuration
    SERVER_NAME: str = "context-engine-mcp"
    SERVER_VERSION: str = "1.0.0"
    
    # HTTP Server Configuration
    HTTP_HOST: str = os.getenv("HTTP_HOST", "localhost")
    HTTP_PORT: int = int(os.getenv("HTTP_PORT", "8091"))
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate(cls) -> None:
        """Validate required configuration."""
        if not cls.ORGANISATION_SERVICE_AUTH_KEY:
            raise ValueError("ORGANISATION_SERVICE_AUTH_KEY is required")
        
        if not cls.ORGANISATION_SERVICE_HOST:
            raise ValueError("ORGANISATION_SERVICE_HOST is required")
    
    @classmethod
    def get_api_base_url(cls) -> str:
        """Get the REST API base URL, respecting existing scheme and adding port if needed."""
        host = cls.ORGANISATION_SERVICE_HOST
        port = cls.ORGANISATION_SERVICE_PORT

        if "://" in host:  # Scheme (http:// or https://) is already present
            # Check if port is already in the URL
            host_part = host.split("://")[1]
            if ":" in host_part:
                # Port already specified in host
                return host
            else:
                # Only add port if it's not the default port for the scheme
                scheme = host.split("://")[0]
                if (scheme == "https" and port == 443) or (scheme == "http" and port == 80):
                    return host
                else:
                    return f"{host}:{port}"
        else:  # No scheme, default to http
            return f"http://{host}:{port}"
    
    @classmethod
    def get_auth_header(cls) -> str:
        """Get authentication header for API calls."""
        return f"{cls.ORGANISATION_SERVICE_AUTH_KEY}"