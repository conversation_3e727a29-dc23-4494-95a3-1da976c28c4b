"""REST API client for Context Service."""

import logging
import aiohttp
import json
from typing import Any, Dict, Optional
from ..helper.config import Config

logger = logging.getLogger(__name__)


class ContextAPIClient:
    """REST API client for Context Service."""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = Config.get_api_base_url()
        
    async def connect(self):
        """Initialize the HTTP session."""
        try:
            # Validate configuration
            Config.validate()
            
            # Create HTTP session with authentication headers
            headers = {
                "X-Org-Auth-Key": Config.get_auth_header(),
                "Content-Type": "application/json"
            }
            
            timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout,
                connector=aiohttp.TCPConnector(limit=100)
            )
            
            logger.info(f"Connected to Context API at {self.base_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Context API: {e}")
            raise
    
    async def close(self):
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
            logger.info("Closed Context API connection")
    
    async def search_similar_documents(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call search API endpoint."""
        try:
            if not self.session:
                await self.connect()
            
            url = f"{self.base_url}/api/v1/google-drive/search"
            logger.debug(f"Constructed URL: {url}, base_url: {self.base_url}")
            
            # Prepare request payload
            payload = {
                "user_id": request_data.get("user_id", ""),
                "query_text": request_data.get("query_text", ""),
                "top_k": request_data.get("top_k", 5),
                "agent_id": request_data.get("agent_id", ""),
                "organisation_id": request_data.get("organisation_id", ""),
                "file_ids": request_data.get("file_ids", [])
            }
            
            logger.debug(f"Making API call to {url} with payload: {payload}")
            logger.debug(f"Session headers: {self.session.headers}")

            async with self.session.post(url, json=payload) as response:
                logger.debug(f"Response status: {response.status}")
                if response.status == 200:
                    try:
                        result = await response.json()
                        logger.info(f"Search API call successful")
                        logger.debug(f"Response data: {result}")
                        return result
                    except Exception as json_error:
                        logger.error(f"Failed to parse JSON response: {json_error}")
                        response_text = await response.text()
                        logger.error(f"Raw response: {response_text}")
                        raise json_error
                else:
                    error_text = await response.text()
                    logger.error(f"Search API call failed with status {response.status}: {error_text}")
                    return {
                        "success": False,
                        "message": f"API error: {response.status} - {error_text}",
                        "results": []
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error in search_similar_documents: {e}")
            return {
                "success": False,
                "message": f"HTTP client error: {str(e)}",
                "results": []
            }
        except Exception as e:
            logger.error(f"Error in search_similar_documents: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "results": []
            }
    
    async def batch_search_similar_documents(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call batch search API endpoint."""
        try:
            if not self.session:
                await self.connect()
            
            url = f"{self.base_url}/api/v1/google-drive/batch-search"
            
            # Prepare request payload
            payload = {
                "user_id": request_data.get("user_id", ""),
                "query_texts": request_data.get("query_texts", []),
                "top_k": request_data.get("top_k", 5),
                "agent_id": request_data.get("agent_id", ""),
                "organisation_id": request_data.get("organisation_id", ""),
                "file_ids": request_data.get("file_ids", [])
            }
            
            logger.debug(f"Making API call to {url} with payload: {payload}")
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"Batch search API call successful")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Batch search API call failed with status {response.status}: {error_text}")
                    return {
                        "success": False,
                        "message": f"API error: {response.status} - {error_text}",
                        "query_results": []
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error in batch_search_similar_documents: {e}")
            return {
                "success": False,
                "message": f"HTTP client error: {str(e)}",
                "query_results": []
            }
        except Exception as e:
            logger.error(f"Error in batch_search_similar_documents: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "query_results": []
            }
    
    async def sync_file_by_url(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call sync file by URL API endpoint."""
        try:
            if not self.session:
                await self.connect()
            
            url = f"{self.base_url}/api/v1/google-drive/sync-file-by-url"
            
            # Prepare request payload
            payload = {
                "drive_url": request_data.get("drive_url", ""),
                "agent_id": request_data.get("agent_id", ""),
                "user_id": request_data.get("user_id", ""),
                "organisation_id": request_data.get("organisation_id", "")
            }
            
            logger.debug(f"Making API call to {url} with payload: {payload}")
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"Upload file API call successful")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Upload file API call failed with status {response.status}: {error_text}")
                    return {
                        "success": False,
                        "message": f"API error: {response.status} - {error_text}",
                        "file_id": "",
                        "file_name": "",
                        "sync_status": "failed"
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error in sync_file_by_url: {e}")
            return {
                "success": False,
                "message": f"HTTP client error: {str(e)}",
                "file_id": "",
                "file_name": "",
                "sync_status": "failed"
            }
        except Exception as e:
            logger.error(f"Error in sync_file_by_url: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "file_id": "",
                "file_name": "",
                "sync_status": "failed"
            }