"""Business logic for Context Engine MCP Server."""

import logging
from typing import Any, Dict

from ..constants.schema import SearchSchema, BatchSearchSchema, UploadFileSchema
from ..providers.api_client import ContextAPIClient

logger = logging.getLogger(__name__)


class ContextEngineService:
    """Service class for handling business logic."""
    
    def __init__(self):
        self.api_client = ContextAPIClient()
    
    async def initialize(self):
        """Initialize the service and connect to API."""
        await self.api_client.connect()
        logger.info("ContextEngineService initialized")
    
    async def cleanup(self):
        """Cleanup resources."""
        await self.api_client.close()
        logger.info("ContextEngineService cleaned up")
    
    async def search_documents(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle search tool request."""
        try:
            # Validate input using Pydantic schema
            schema = SearchSchema(**arguments)
            
            # Convert to dict for API call
            request_data = schema.model_dump()
            
            # Call REST API service
            result = await self.api_client.search_similar_documents(request_data)
            
            logger.info(f"Search completed for user {schema.user_id}, query: {schema.query_text}")
            return result
            
        except Exception as e:
            logger.error(f"Error in search_documents: {e}")
            return {
                "success": False,
                "message": f"Validation or processing error: {str(e)}",
                "results": []
            }
    
    async def batch_search_documents(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle batch search tool request."""
        try:
            # Validate input using Pydantic schema
            schema = BatchSearchSchema(**arguments)
            
            # Convert to dict for API call
            request_data = schema.model_dump()
            
            # Call REST API service
            result = await self.api_client.batch_search_similar_documents(request_data)
            
            logger.info(f"Batch search completed for user {schema.user_id}, {len(schema.query_texts)} queries")
            return result
            
        except Exception as e:
            logger.error(f"Error in batch_search_documents: {e}")
            return {
                "success": False,
                "message": f"Validation or processing error: {str(e)}",
                "query_results": []
            }
    
    async def upload_file_by_url(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle upload file by URL tool request."""
        try:
            # Validate input using Pydantic schema
            schema = UploadFileSchema(**arguments)
            
            # Convert to dict for API call
            request_data = schema.model_dump()
            
            # Call REST API service
            result = await self.api_client.sync_file_by_url(request_data)
            
            logger.info(f"File upload initiated for agent {schema.agent_id}, URL: {schema.drive_url}")
            return result
            
        except Exception as e:
            logger.error(f"Error in upload_file_by_url: {e}")
            return {
                "success": False,
                "message": f"Validation or processing error: {str(e)}",
                "file_id": "",
                "file_name": "",
                "sync_status": "failed"
            }