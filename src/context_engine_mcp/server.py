"""HTTP-based MCP server implementation using FastAPI and SSE."""

import json
import logging
import async<PERSON>
from typing import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi_mcp import FastApiMCP
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import StreamingResponse
from sse_starlette import EventSourceResponse
from pydantic import ValidationError
import mcp.types as types
from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server

from .constants.enum import Tools
from .constants.schema import SearchSchema, BatchSearchSchema, UploadFileSchema
from .services.context_service import ContextEngineService
from .helper.config import Config

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Global service instance
context_service = ContextEngineService()
_service_initialized = False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    global _service_initialized
    try:
        # Initialize the service
        await context_service.initialize()
        _service_initialized = True
        logger.info(f"Starting {Config.SERVER_NAME} v{Config.SERVER_VERSION} HTTP server")
        yield
    finally:
        # Clean up resources
        _service_initialized = False
        await context_service.cleanup()
        logger.info("HTTP server shutdown complete")


# Create FastAPI app
app = FastAPI(
    title=Config.SERVER_NAME,
    version=Config.SERVER_VERSION,
    description="MCP server for Context Engine integration with REST API service",
    lifespan=lifespan
)


class MCPHTTPServer:
    """HTTP-based MCP server using SSE transport."""
    
    def __init__(self):
        self.server = Server(Config.SERVER_NAME)
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Set up MCP server handlers."""
        
        @self.server.list_tools()
        async def handle_list_tools() -> list[types.Tool]:
            """List available MCP tools."""
            return [
                types.Tool(
                    name=Tools.SEARCH,
                    description="Search for documents semantically similar to a query",
                    inputSchema=SearchSchema.model_json_schema(),
                ),
                types.Tool(
                    name=Tools.BATCH_SEARCH,
                    description="Batch search for documents semantically similar to multiple queries",
                    inputSchema=BatchSearchSchema.model_json_schema(),
                ),
                types.Tool(
                    name=Tools.UPLOAD_FILE_BY_URL,
                    description="Sync a specific Google Drive file by URL",
                    inputSchema=UploadFileSchema.model_json_schema(),
                ),
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict | None) -> list[types.TextContent]:
            """Handle tool calls."""
            try:
                # Check if service is initialized
                if not _service_initialized:
                    error_response = {"message": "Service not yet initialized, please wait", "is_error": True}
                    return [types.TextContent(type="text", text=json.dumps(error_response, indent=2))]

                # Ensure service is initialized
                if not context_service.api_client.session:
                    await context_service.initialize()

                logger.info(f"Calling tool: {name} with arguments: {arguments}")
                
                # Route to appropriate service method
                match name:
                    case Tools.SEARCH:
                        result = await context_service.search_documents(arguments or {})
                    case Tools.BATCH_SEARCH:
                        result = await context_service.batch_search_documents(arguments or {})
                    case Tools.UPLOAD_FILE_BY_URL:
                        result = await context_service.upload_file_by_url(arguments or {})
                    case _:
                        error_response = {"message": f"Unknown tool: {name}", "is_error": True}
                        return [types.TextContent(type="text", text=json.dumps(error_response, indent=2))]
                
                # Return result as JSON
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
                
            except Exception as error:
                logger.error(f"Error handling tool call: {error}", exc_info=True)
                error_response = {"message": str(error), "is_error": True}
                return [types.TextContent(type="text", text=json.dumps(error_response, indent=2))]


# Global MCP server instance
mcp_server = MCPHTTPServer()


class SSETransport:
    """SSE transport implementation for MCP."""
    
    def __init__(self):
        self.message_queue = asyncio.Queue()
        self.response_queue = asyncio.Queue()
    
    async def send_message(self, message: dict):
        """Send a message through the transport."""
        await self.message_queue.put(message)
    
    async def receive_response(self) -> dict:
        """Receive a response from the transport."""
        return await self.response_queue.get()
    
    async def process_messages(self):
        """Process incoming messages and generate responses."""
        while True:
            try:
                message = await self.message_queue.get()
                logger.debug(f"Processing message: {message}")
                
                # Handle different message types
                if message.get("method") == "tools/list":
                    tools = await mcp_server.server._handle_list_tools()
                    response = {
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {"tools": [tool.model_dump() for tool in tools]}
                    }
                elif message.get("method") == "tools/call":
                    params = message.get("params", {})
                    name = params.get("name")
                    arguments = params.get("arguments")
                    
                    result = await mcp_server.server._handle_call_tool(name, arguments)
                    response = {
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {"content": [content.model_dump() for content in result]}
                    }
                elif message.get("method") == "initialize":
                    response = {
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "tools": {},
                                "logging": {}
                            },
                            "serverInfo": {
                                "name": Config.SERVER_NAME,
                                "version": Config.SERVER_VERSION
                            }
                        }
                    }
                else:
                    response = {
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {message.get('method')}"
                        }
                    }
                
                await self.response_queue.put(response)
                
            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)
                error_response = {
                    "jsonrpc": "2.0",
                    "id": message.get("id"),
                    "error": {
                        "code": -32603,
                        "message": f"Internal error: {str(e)}"
                    }
                }
                await self.response_queue.put(error_response)


# Global transport instance
# transport = SSETransport()


@app.get("/")
async def root():
    """Root endpoint with server information."""
    return {
        "name": Config.SERVER_NAME,
        "version": Config.SERVER_VERSION,
        "description": "MCP server for Context Engine integration with REST API endpoints",
        "transport": "sse",
        "endpoints": {
            "sse": "/sse",
            "health": "/health",
            "message": "/message",
            "api": {
                "search": "/api/search",
                "batch_search": "/api/batch-search",
                "upload_file": "/api/upload-file"
            }
        },
        "tools": [
            {
                "name": "search",
                "description": "Search for documents semantically similar to a query",
                "endpoint": "/api/search"
            },
            {
                "name": "batch_search",
                "description": "Batch search for documents semantically similar to multiple queries",
                "endpoint": "/api/batch-search"
            },
            {
                "name": "upload_file_by_url",
                "description": "Sync a specific Google Drive file by URL",
                "endpoint": "/api/upload-file"
            }
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "server": Config.SERVER_NAME, "version": Config.SERVER_VERSION}


# @app.post("/message")
# async def send_message(request: Request):
#     """Send a message to the MCP server."""
#     try:
#         message = await request.json()
#         await transport.send_message(message)
#         response = await transport.receive_response()
#         return response
#     except Exception as e:
#         logger.error(f"Error handling message: {e}", exc_info=True)
#         raise HTTPException(status_code=500, detail=str(e))


# @app.get("/sse")
# async def sse_endpoint(request: Request):
#     """SSE endpoint for MCP communication."""
    
#     async def event_generator() -> AsyncGenerator[str, None]:
#         """Generate SSE events."""
#         try:
#             # Start processing messages
#             asyncio.create_task(transport.process_messages())
            
#             # Send initial connection event
#             yield f"data: {json.dumps({'type': 'connection', 'status': 'connected'})}\n\n"
            
#             while True:
#                 try:
#                     # Check if client disconnected
#                     if await request.is_disconnected():
#                         logger.info("Client disconnected from SSE")
#                         break
                    
#                     # Wait for responses with timeout
#                     try:
#                         response = await asyncio.wait_for(transport.receive_response(), timeout=1.0)
#                         yield f"data: {json.dumps(response)}\n\n"
#                     except asyncio.TimeoutError:
#                         # Send keepalive
#                         yield f"data: {json.dumps({'type': 'keepalive'})}\n\n"
                        
#                 except Exception as e:
#                     logger.error(f"Error in SSE stream: {e}", exc_info=True)
#                     yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
#                     break
                    
#         except Exception as e:
#             logger.error(f"Fatal error in SSE generator: {e}", exc_info=True)
#             yield f"data: {json.dumps({'type': 'error', 'message': 'Server error'})}\n\n"
    
#     return EventSourceResponse(event_generator())


# REST API Endpoints for the three tools
@app.post("/api/search")
async def api_search(search_request: SearchSchema):
    """REST API endpoint for search tool."""
    try:
        # Check if service is initialized
        if not _service_initialized:
            raise HTTPException(status_code=503, detail="Service not yet initialized, please wait")

        # Ensure service is initialized
        if not context_service.api_client.session:
            await context_service.initialize()

        logger.info(f"API search called with: {search_request.model_dump()}")

        # Call the service method
        result = await context_service.search_documents(search_request.model_dump())

        return {"success": True, "data": result}

    except ValidationError as e:
        logger.error(f"Validation error in API search: {e}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error in API search: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/batch-search")
async def api_batch_search(batch_search_request: BatchSearchSchema):
    """REST API endpoint for batch search tool."""
    try:
        # Check if service is initialized
        if not _service_initialized:
            raise HTTPException(status_code=503, detail="Service not yet initialized, please wait")

        # Ensure service is initialized
        if not context_service.api_client.session:
            await context_service.initialize()

        logger.info(f"API batch search called with: {batch_search_request.model_dump()}")

        # Call the service method
        result = await context_service.batch_search_documents(batch_search_request.model_dump())

        return {"success": True, "data": result}

    except ValidationError as e:
        logger.error(f"Validation error in API batch search: {e}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error in API batch search: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload-file")
async def api_upload_file(upload_request: UploadFileSchema):
    """REST API endpoint for upload file by URL tool."""
    try:
        # Check if service is initialized
        if not _service_initialized:
            raise HTTPException(status_code=503, detail="Service not yet initialized, please wait")

        # Ensure service is initialized
        if not context_service.api_client.session:
            await context_service.initialize()

        logger.info(f"API upload file called with: {upload_request.model_dump()}")

        # Call the service method
        result = await context_service.upload_file_by_url(upload_request.model_dump())

        return {"success": True, "data": result}

    except ValidationError as e:
        logger.error(f"Validation error in API upload file: {e}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error in API upload file: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


def main():
    """Main entry point for the HTTP server."""
    import uvicorn
    try:
        Config.validate()
        logger.info(f"Starting {Config.SERVER_NAME} HTTP server on {Config.HTTP_HOST}:{Config.HTTP_PORT}")

        # Create and mount the MCP server with proper initialization
        mcp = FastApiMCP(app)

        # Mount the MCP server directly to your FastAPI app
        mcp.mount()
        uvicorn.run(
            app,
            host=Config.HTTP_HOST,
            port=Config.HTTP_PORT,
            reload=False,
            log_level=Config.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("\nServer stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise


if __name__ == "__main__":
    main()