# Context Engine MCP Server

A Model Context Protocol (MCP) server that provides tools for interacting with the Context Engine service via gRPC. This server exposes three tools for document search and file synchronization using HTTP/SSE transport.

## Features

- **search**: Search for documents semantically similar to a query
- **batch_search**: Batch search for multiple queries simultaneously
- **upload_file_by_url**: Sync a Google Drive file by URL
- **HTTP/SSE Transport**: Accessible via HTTP endpoints with Server-Sent Events
- **Modern Tooling**: Built with UV for fast dependency management and Docker support

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│   HTTP Server   │───▶│ ContextService  │
│                 │    │   (FastAPI)     │    │    (gRPC)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Project Structure

```
context-engine-mcp/
├── src/
│   └── context_engine_mcp/
│       ├── __init__.py              # Main entry point
│       ├── server.py                # HTTP/SSE MCP server implementation
│       ├── constants/
│       │   ├── __init__.py
│       │   ├── enum.py              # Enums for tools and types
│       │   └── schema.py            # Pydantic schemas for validation
│       ├── services/
│       │   ├── __init__.py
│       │   └── context_service.py   # Business logic implementation
│       ├── providers/
│       │   ├── __init__.py
│       │   └── context_service.py   # gRPC client implementation
│       └── helper/
│           ├── __init__.py
│           └── config.py            # Configuration management
├── grpc_generated/                  # Generated gRPC Python code
│   ├── __init__.py
│   ├── context_pb2.py
│   └── context_pb2_grpc.py
├── proto/
│   └── context.proto                # Protocol buffer definitions
├── roocode/                         # Test scripts and development tools
├── memory-bank/                     # Project context and documentation
├── pyproject.toml                   # Package configuration with UV support
├── uv.lock                          # UV lock file for reproducible builds
├── Dockerfile                       # Optimized Docker build
├── .dockerignore                    # Docker build context exclusions
├── mcp_config.json                  # MCP client configuration
├── .env.example                     # Environment variables template
└── README.md                        # This file
```

## Prerequisites

- Python 3.10+
- Access to the ContextService gRPC endpoint
- Required environment variables configured

## Installation

### Method 1: Using uvx (Recommended for end users)

```bash
uvx context-engine-mcp
```

### Method 2: Using uv (Development)

```bash
git clone <repository-url>
cd context-engine-mcp
uv sync
uv run context-engine-mcp
```

### Method 3: Using Docker

```bash
docker build -t context-engine-mcp .
docker run -it --env-file .env -p 8000:8000 context-engine-mcp
```

### Method 4: Traditional pip (Fallback)

```bash
git clone <repository-url>
cd context-engine-mcp
pip install -e .
context-engine-mcp
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Organisation Service Configuration
ORGANISATION_SERVICE_HOST=localhost
ORGANISATION_SERVICE_PORT=50070
ORGANISATION_SERVICE_AUTH_KEY=your_auth_key_here

# HTTP Server Configuration
HTTP_HOST=localhost
HTTP_PORT=8000

# Redis Configuration (optional)
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# Logging Configuration
LOG_LEVEL=INFO
```

### MCP Configuration

#### Claude Desktop with uvx
Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "context-engine-mcp": {
      "command": "uvx",
      "args": ["context-engine-mcp"],
      "env": {
        "ORGANISATION_SERVICE_HOST": "localhost",
        "ORGANISATION_SERVICE_PORT": "50070",
        "ORGANISATION_SERVICE_AUTH_KEY": "your_auth_key_here"
      }
    }
  }
}
```

#### Claude Desktop with Docker
```json
{
  "mcpServers": {
    "context-engine-mcp": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i",
        "--mount", "type=bind,src=/Users/<USER>/Users/<USER>",
        "--env-file", "/path/to/.env",
        "-p", "8000:8000",
        "context-engine-mcp"
      ]
    }
  }
}
```

#### Development Configuration
```json
{
  "mcpServers": {
    "context-engine-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/context-engine-mcp",
        "run",
        "context-engine-mcp"
      ]
    }
  }
}
```

## Usage

### Server Access

The server runs as an HTTP server accessible at:
- **Default**: `http://localhost:8000`
- **Health Check**: `http://localhost:8000/health`
- **Server Info**: `http://localhost:8000/`
- **SSE Endpoint**: `http://localhost:8000/sse`
- **Message Endpoint**: `http://localhost:8000/message`

### Available Tools

#### 1. search

Search for documents semantically similar to a query.

**Parameters:**
- `user_id` (required): User ID
- `query_text` (required): Search query text
- `organisation_id` (required): Organization ID
- `top_k` (optional): Number of results to return (default: 5, max: 100)
- `agent_id` (optional): Agent ID for department filtering
- `file_ids` (optional): List of specific file IDs to search within

**Example:**
```json
{
  "user_id": "user123",
  "query_text": "machine learning algorithms",
  "organisation_id": "org456",
  "top_k": 5
}
```

#### 2. batch_search

Batch search for documents semantically similar to multiple queries.

**Parameters:**
- `user_id` (required): User ID
- `query_texts` (required): Array of search query texts
- `organisation_id` (required): Organization ID
- `top_k` (optional): Number of results per query (default: 5, max: 100)
- `agent_id` (optional): Agent ID for department filtering
- `file_ids` (optional): List of specific file IDs to search within

**Example:**
```json
{
  "user_id": "user123",
  "query_texts": ["machine learning", "data science", "AI"],
  "organisation_id": "org456",
  "top_k": 3
}
```

#### 3. upload_file_by_url

Sync a Google Drive file by URL.

**Parameters:**
- `drive_url` (required): Google Drive URL
- `agent_id` (required): Agent ID
- `organisation_id` (required): Organisation ID
- `user_id` (optional): User ID

**Example:**
```json
{
  "drive_url": "https://drive.google.com/file/d/**********/view",
  "agent_id": "agent789",
  "organisation_id": "org456",
  "user_id": "user123"
}
```

## Testing

### Using MCP Inspector

```bash
npx @modelcontextprotocol/inspector uvx context-engine-mcp
```

### Development Testing

```bash
cd /path/to/context-engine-mcp
npx @modelcontextprotocol/inspector uv run context-engine-mcp
```

### HTTP Endpoint Testing

```bash
# Health check
curl http://localhost:8000/health

# Server info
curl http://localhost:8000/

# Send MCP message
curl -X POST http://localhost:8000/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
```

## Development

### Using UV (Recommended)

```bash
# Install dependencies
uv sync

# Run in development mode
uv run context-engine-mcp

# Run tests
uv run python roocode/test_mcp_server.py

# Code formatting
uv run black src/
uv run isort src/
uv run ruff check src/
```

### Regenerating gRPC Code

If you modify the proto file, regenerate the Python gRPC code:

```bash
uv run python -m grpc_tools.protoc --python_out=grpc_generated --grpc_python_out=grpc_generated --proto_path=proto proto/context.proto
```

Then fix the import in the generated file:
```bash
# In grpc_generated/context_pb2_grpc.py, change:
# import context_pb2 as context__pb2
# to:
# from . import context_pb2 as context__pb2
```

### Adding New Tools

1. Add the new RPC method to `proto/context.proto`
2. Regenerate the gRPC code
3. Add the tool enum to `src/context_engine_mcp/constants/enum.py`
4. Create a Pydantic schema in `src/context_engine_mcp/constants/schema.py`
5. Add the gRPC method to `src/context_engine_mcp/providers/context_service.py`
6. Add the service method to `src/context_engine_mcp/services/context_service.py`
7. Register the tool in `src/context_engine_mcp/server.py`

## Authentication

The server uses Bearer token authentication for gRPC calls. The authentication key is passed in the `authorization` metadata header:

```
authorization: Bearer <ORGANISATION_SERVICE_AUTH_KEY>
```

## Error Handling

The server includes comprehensive error handling:
- Input validation using Pydantic schemas
- gRPC connection failures
- Authentication errors
- Service unavailability
- Structured error responses

All errors are logged and returned as structured JSON responses to the MCP client.

## Logging

The server uses Python's built-in logging module with configurable levels. Logs include:
- Service initialization and cleanup
- Tool call requests and responses
- gRPC connection status
- Error details with stack traces

## Docker Support

### Build & Run

```bash
# Build the image
docker build -t context-engine-mcp .

# Run with environment file
docker run -it --env-file .env -p 8000:8000 context-engine-mcp

# Run with individual environment variables
docker run -it \
  -e ORGANISATION_SERVICE_HOST=localhost \
  -e ORGANISATION_SERVICE_PORT=50070 \
  -e ORGANISATION_SERVICE_AUTH_KEY=your_key \
  -p 8000:8000 \
  context-engine-mcp
```

### Health Check

The Docker container includes a health check that verifies the HTTP server is responding:

```bash
# Check container health
docker ps

# Manual health check
curl http://localhost:8000/health
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the modular architecture pattern
4. Add tests for new functionality
5. Ensure code quality with black, isort, and ruff
6. Submit a pull request

## License

MIT License - see LICENSE file for details.
