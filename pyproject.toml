[project]
name = "context-engine-mcp"
version = "1.0.0"
description = "MCP server for Context Engine integration with REST API service"
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.10"
authors = [
    { name = "Context Engine Team", email = "<EMAIL>" }
]
keywords = ["mcp", "rest-api", "context-engine", "search", "semantic-search"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]

dependencies = [
    "mcp>=1.9.4",
    "aiohttp>=3.9.0",
    "redis>=5.0.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "sse-starlette>=1.6.0",
    "fastapi-mcp>=0.3.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]

[project.scripts]
context-engine-mcp = "context_engine_mcp:main"

[project.urls]
Homepage = "https://github.com/your-org/context-engine-mcp"
Repository = "https://github.com/your-org/context-engine-mcp"
Issues = "https://github.com/your-org/context-engine-mcp/issues"
Documentation = "https://github.com/your-org/context-engine-mcp#readme"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/context_engine_mcp"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/proto",
    "/README.md",
    "/pyproject.toml",
]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]

[tool.black]
line-length = 100
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.ruff]
line-length = 100
target-version = "py310"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
