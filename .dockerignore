# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
roocode/
memory-bank/
*.log
.pytest_cache/
.coverage
.mypy_cache/
.ruff_cache/

# Git
.git/
.gitignore

# Documentation (except README.md which is needed for package metadata)
*.md
!README.md

# Development files
requirements.txt
mcp_server.py
mcp_config.json

# UV cache
.uv-cache/

# Test files
tests/
test_*.py
*_test.py